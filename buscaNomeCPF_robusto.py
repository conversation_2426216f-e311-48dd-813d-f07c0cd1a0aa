import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os
import subprocess
import sys

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF - SeleniumBase UC Mode ULTRA ROBUSTO")
        self.master.geometry("800x600")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF - UC Mode ULTRA ROBUSTO", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha Excel (primeira coluna = números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

        # Status e logs
        self.status_label = ttk.Label(main_frame, text="Pronto para iniciar", font=('Arial', 10))
        self.status_label.grid(column=0, row=8, columnspan=2, pady=10)

        ttk.Label(main_frame, text="Logs:", font=('Arial', 12, 'bold')).grid(
            column=0, row=9, sticky=tk.W, pady=(20, 5))
        
        self.log_text = tk.Text(main_frame, height=8, width=80, bg="#34495e", fg="white")
        self.log_text.grid(column=0, row=10, columnspan=2, pady=5, sticky=(tk.W, tk.E))

        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.log_text.yview)
        scrollbar.grid(column=2, row=10, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=scrollbar.set)

    def log_message(self, message):
        """Adiciona mensagem ao log visual"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.master.update()

    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror("Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror("Erro", "Por favor, preencha o usuário e senha.")
            return

        self.log_text.delete(1.0, tk.END)
        self.log_message("Iniciando processamento...")
        
        t = threading.Thread(target=self.processar_planilha, args=(usuario, senha))
        t.start()

    def processar_planilha(self, usuario, senha):
        """Processa a planilha usando SeleniumBase UC Mode"""
        try:
            self.log_message("Carregando planilha...")
            
            # Carregar planilha
            df = pd.read_excel(self.excel_dir)
            
            if df.empty or len(df.columns) == 0:
                self.log_message("❌ Planilha vazia!")
                messagebox.showerror("Erro", "A planilha está vazia ou não possui colunas.")
                return
            
            primeira_coluna = df.columns[0]
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})
            df = df.dropna(subset=['Número do Processo'])
            
            if len(df) == 0:
                self.log_message("❌ Nenhum processo válido encontrado!")
                messagebox.showerror("Erro", "Não há números de processo válidos na primeira coluna.")
                return

            processos = df['Número do Processo'].tolist()
            self.log_message(f"✅ {len(processos)} processos carregados")

            # Criar script SeleniumBase mais robusto
            self.log_message("Criando script SeleniumBase...")
            
            script_content = f'''
from seleniumbase import BaseCase
import pandas as pd
from datetime import datetime
import os
import time
import random
import json

class TestBuscaProcessos(BaseCase):

    def test_buscar_processos(self):
        """Busca processos usando UC Mode ULTRA ROBUSTO com máxima anti-detecção"""

        print("🚀 INICIANDO SeleniumBase UC Mode ULTRA ROBUSTO...")

        # Lista de processos
        processos = {processos}
        resultados = []

        try:
            # Configurações avançadas de stealth
            print("🛡️ Aplicando configurações avançadas de anti-detecção...")

            # Executar scripts de stealth avançados
            self.execute_script("""
                // Remover propriedades de detecção de webdriver
                Object.defineProperty(navigator, 'webdriver', {{
                    get: () => undefined,
                }});

                // Mascarar plugins
                Object.defineProperty(navigator, 'plugins', {{
                    get: () => [1, 2, 3, 4, 5],
                }});

                // Mascarar languages
                Object.defineProperty(navigator, 'languages', {{
                    get: () => ['pt-BR', 'pt', 'en-US', 'en'],
                }});

                // Remover chrome runtime
                window.chrome = {{
                    runtime: {{}}
                }};

                // Mascarar permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({{ state: Notification.permission }}) :
                        originalQuery(parameters)
                );
            """)

            print("✅ Scripts de stealth aplicados!")

            # Login com retry e delays aleatórios
            print("🔐 FAZENDO LOGIN COM ANTI-DETECÇÃO...")
            login_sucesso = False

            for tentativa in range(5):  # Aumentado para 5 tentativas
                try:
                    print(f"🔄 Tentativa de login {{tentativa + 1}}/5...")

                    # Delay aleatório entre tentativas
                    if tentativa > 0:
                        delay = random.uniform(3, 8)
                        print(f"⏳ Aguardando {{delay:.1f}}s antes da próxima tentativa...")
                        self.sleep(delay)

                    # Abrir página com reconexão robusta
                    self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=8)

                    # Delay aleatório após carregamento
                    delay_inicial = random.uniform(2, 5)
                    print(f"⏳ Aguardando carregamento: {{delay_inicial:.1f}}s...")
                    self.sleep(delay_inicial)

                    # Verificar se a página carregou corretamente
                    if not self.is_element_present('//*[@id="login"]', timeout=10):
                        print(f"⚠️ Tentativa {{tentativa + 1}}: Campo de login não encontrado")
                        continue

                    # Simular comportamento humano no preenchimento
                    print("👤 Simulando digitação humana...")

                    # Limpar campo e digitar usuário com delays
                    self.clear('//*[@id="login"]')
                    for char in "{usuario}":
                        self.type('//*[@id="login"]', char)
                        self.sleep(random.uniform(0.05, 0.15))

                    # Delay entre campos
                    self.sleep(random.uniform(0.5, 1.2))

                    # Limpar campo e digitar senha com delays
                    self.clear('//*[@id="senha"]')
                    for char in "{senha}":
                        self.type('//*[@id="senha"]', char)
                        self.sleep(random.uniform(0.05, 0.15))

                    # Delay antes de clicar
                    self.sleep(random.uniform(0.8, 1.5))

                    # Clicar no botão de login
                    self.click('//*[@id="formLogin"]/div[4]/input[1]')

                    # Aguardar processamento do login
                    delay_login = random.uniform(4, 8)
                    print(f"⏳ Aguardando processamento do login: {{delay_login:.1f}}s...")
                    self.sleep(delay_login)

                    # Verificar se o login foi bem-sucedido
                    if self.is_element_present('//*[@id="menuPrinciapl"]/ul[2]/li', timeout=10):
                        print("✅ LOGIN REALIZADO COM SUCESSO!")
                        login_sucesso = True
                        break
                    else:
                        print(f"❌ Tentativa {{tentativa + 1}}: Login falhou - verificando página...")

                        # Verificar se há mensagem de erro específica
                        page_source = self.get_page_source()
                        if "predatório" in page_source.lower():
                            print("🚨 ACESSO PREDATÓRIO DETECTADO NO LOGIN!")
                            # Aguardar mais tempo antes da próxima tentativa
                            self.sleep(random.uniform(10, 20))

                except Exception as e:
                    print(f"❌ Tentativa {{tentativa + 1}} falhou: {{e}}")
                    if tentativa == 4:  # Última tentativa
                        raise Exception("❌ FALHA CRÍTICA: Login falhou após 5 tentativas")

            if not login_sucesso:
                raise Exception("❌ FALHA CRÍTICA: Não foi possível fazer login")

            # Processar cada processo com máxima proteção
            print(f"📋 INICIANDO PROCESSAMENTO DE {{len(processos)}} PROCESSOS...")

            for i, processo in enumerate(processos, 1):
                print(f"\\n🔍 PROCESSANDO {{i}}/{{len(processos)}}: {{processo}}")

                resultado = {{
                    'Número do Processo': processo,
                    'Processo Encontrado': 'Não',
                    'Nome Polo Ativo': '',
                    'CPF Polo Ativo': ''
                }}

                try:
                    # Verificar integridade do browser
                    try:
                        current_handle = self.driver.current_window_handle
                        print("✅ Browser ativo e funcionando")
                    except:
                        print("🚨 ERRO: Browser foi fechado! Tentando reconectar...")
                        self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=5)
                        resultado['Processo Encontrado'] = 'Erro - Browser Fechado'
                        resultados.append(resultado)
                        continue

                    # Delay aleatório entre processos para evitar detecção
                    if i > 1:
                        delay_entre_processos = random.uniform(2, 6)
                        print(f"⏳ Delay anti-detecção: {{delay_entre_processos:.1f}}s...")
                        self.sleep(delay_entre_processos)

                    # Navegar para busca com retry robusto
                    busca_carregada = False
                    for tentativa_busca in range(3):
                        try:
                            print(f"🌐 Carregando página de busca (tentativa {{tentativa_busca + 1}}/3)...")
                            self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24", reconnect_time=5)

                            # Aguardar carregamento
                            self.sleep(random.uniform(2, 4))

                            if self.is_element_present('//*[@id="ProcessoNumero"]', timeout=8):
                                print("✅ Página de busca carregada!")
                                busca_carregada = True
                                break
                            else:
                                print(f"⚠️ Tentativa {{tentativa_busca + 1}}: Campo de busca não encontrado")

                        except Exception as e:
                            print(f"❌ Tentativa {{tentativa_busca + 1}} falhou: {{e}}")
                            if tentativa_busca == 2:
                                print(f"🚨 ERRO CRÍTICO: Falha ao carregar página de busca para {{processo}}")
                                resultado['Processo Encontrado'] = 'Erro - Página não carregou'
                                break

                    if not busca_carregada:
                        resultados.append(resultado)
                        continue

                    # Limpar campo e inserir número do processo com simulação humana
                    print("📝 Inserindo número do processo...")
                    self.clear('//*[@id="ProcessoNumero"]')
                    self.sleep(random.uniform(0.3, 0.8))

                    # Digitar processo caractere por caractere
                    for char in str(processo):
                        self.type('//*[@id="ProcessoNumero"]', char)
                        self.sleep(random.uniform(0.08, 0.2))

                    self.sleep(random.uniform(0.5, 1.2))

                    # Tentar clicar em "mostrar todos os processos" se disponível
                    try:
                        if self.is_element_present('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]', timeout=3):
                            print("🔘 Clicando em 'Mostrar todos os processos'...")
                            self.click('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]')
                            self.sleep(random.uniform(0.5, 1.0))
                    except:
                        print("ℹ️ Botão 'Mostrar todos' não encontrado ou não clicável")

                    # Clicar em pesquisar
                    print("🔍 Executando pesquisa...")
                    self.click('//*[@id="divBotoesCentralizados"]/input[1]')

                    # Aguardar resultado com timeout maior
                    delay_pesquisa = random.uniform(4, 8)
                    print(f"⏳ Aguardando resultado: {{delay_pesquisa:.1f}}s...")
                    self.sleep(delay_pesquisa)

                    # Verificar resultado da pesquisa
                    print("📊 Analisando resultado...")
                    try:
                        page_source = self.get_page_source()

                        # Verificar se há mensagem de acesso predatório
                        if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                            print(f"🚨 PROCESSO {{processo}}: ACESSO PREDATÓRIO DETECTADO!")
                            resultado['Processo Encontrado'] = 'Erro - Acesso Predatório'

                            # Aguardar mais tempo quando detectado
                            delay_predatorio = random.uniform(15, 30)
                            print(f"⏳ Aguardando {{delay_predatorio:.1f}}s devido à detecção...")
                            self.sleep(delay_predatorio)

                        elif "Polo Ativo" in page_source or self.is_text_visible("Polo Ativo"):
                            resultado['Processo Encontrado'] = 'Sim'
                            print(f"✅ PROCESSO {{processo}}: ENCONTRADO!")

                            # Extrair dados com mais robustez
                            try:
                                print("📋 Extraindo dados do processo...")
                                spans = self.find_elements("span")
                                tds = self.find_elements("td")

                                # Combinar elementos para busca
                                elementos = spans + tds

                                for elemento in elementos:
                                    try:
                                        text = elemento.text.strip()
                                        if text and len(text) > 3:
                                            # Buscar nome (sem números no início)
                                            if not resultado['Nome Polo Ativo'] and not any(char.isdigit() for char in text[:3]) and len(text) > 5:
                                                # Filtrar textos que parecem ser nomes
                                                if not any(palavra in text.lower() for palavra in ['processo', 'número', 'data', 'valor', 'status']):
                                                    resultado['Nome Polo Ativo'] = text[:100]  # Limitar tamanho

                                            # Buscar CPF (formato XXX.XXX.XXX-XX)
                                            elif "." in text and "-" in text and len(text) >= 11 and len(text) <= 14:
                                                if text.count('.') == 2 and text.count('-') == 1:
                                                    resultado['CPF Polo Ativo'] = text
                                    except:
                                        continue

                                print(f"📋 Nome extraído: {{resultado['Nome Polo Ativo'][:50] if resultado['Nome Polo Ativo'] else 'Não encontrado'}}")
                                print(f"📋 CPF extraído: {{resultado['CPF Polo Ativo'] if resultado['CPF Polo Ativo'] else 'Não encontrado'}}")

                            except Exception as e:
                                print(f"⚠️ Erro na extração de dados: {{e}}")

                        else:
                            print(f"ℹ️ PROCESSO {{processo}}: NÃO ENCONTRADO")
                            resultado['Processo Encontrado'] = 'Não'

                    except Exception as e:
                        print(f"❌ Erro ao verificar resultado para {{processo}}: {{e}}")
                        resultado['Processo Encontrado'] = 'Erro - Falha na verificação'

                except Exception as e:
                    print(f"❌ ERRO GERAL no processo {{processo}}: {{e}}")
                    resultado['Processo Encontrado'] = 'Erro - Falha geral'

                resultados.append(resultado)
                print(f"✅ Processo {{processo}} processado: {{resultado['Processo Encontrado']}}")

                # Salvar a cada 3 processos para evitar perda de dados
                if i % 3 == 0:
                    print(f"💾 Salvamento intermediário ({{i}} processos)...")
                    self.salvar_resultados(resultados)

            # Salvar resultados finais
            print("💾 Salvando resultados finais...")
            self.salvar_resultados(resultados)
            print("🎉 PROCESSAMENTO CONCLUÍDO COM SUCESSO!")

        except Exception as e:
            print(f"🚨 ERRO CRÍTICO: {{e}}")
            if resultados:
                print("💾 Salvando resultados parciais...")
                self.salvar_resultados(resultados)

    def salvar_resultados(self, resultados):
        """Salva os resultados em Excel com tratamento de erro"""
        try:
            df_resultado = pd.DataFrame(resultados)
            nome_arquivo = f"Resultados_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}.xlsx"
            df_resultado.to_excel(nome_arquivo, index=False)
            print(f"💾 SALVANDO: Resultados salvos em: {{nome_arquivo}}")
            print(f"📊 Total de {{len(resultados)}} processos salvos")
        except Exception as e:
            print(f"❌ ERRO ao salvar: {{e}}")
            # Tentar salvar como CSV em caso de erro
            try:
                nome_csv = f"Resultados_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}.csv"
                df_resultado.to_csv(nome_csv, index=False, encoding='utf-8-sig')
                print(f"💾 BACKUP: Resultados salvos como CSV: {{nome_csv}}")
            except:
                print("❌ FALHA CRÍTICA: Não foi possível salvar os resultados")
'''

            # Salvar script
            script_filename = 'test_busca_robusta.py'
            with open(script_filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.log_message("Executando SeleniumBase UC Mode...")
            self.status_label.config(text="🚀 Chrome abrindo com UC Mode...")
            
            # Executar script com UC Mode ULTRA ROBUSTO
            cmd = [
                sys.executable, '-m', 'pytest',
                '--uc',                    # UC Mode (undetected-chromedriver)
                '--uc-cdp-events',         # Capturar eventos CDP
                '--incognito',             # Modo incógnito
                '--disable-csp',           # Desabilitar Content Security Policy
                '--disable-ws',            # Desabilitar Web Security
                '--block-images',          # Bloquear imagens para performance
                '--chromium-arg=--disable-blink-features=AutomationControlled',  # Anti-detecção
                '--chromium-arg=--disable-dev-shm-usage',                       # Estabilidade
                '--chromium-arg=--no-first-run',                                # Sem primeira execução
                '--chromium-arg=--disable-default-apps',                        # Sem apps padrão
                '--chromium-arg=--disable-extensions-file-access-check',        # Sem verificação de extensões
                '--chromium-arg=--disable-background-timer-throttling',         # Performance
                '--chromium-arg=--disable-backgrounding-occluded-windows',      # Performance
                '--chromium-arg=--disable-renderer-backgrounding',              # Performance
                '--chromium-arg=--disable-features=TranslateUI',                # Sem tradução
                '--chromium-arg=--disable-ipc-flooding-protection',             # Anti-detecção
                '--chromium-arg=--disable-hang-monitor',                        # Estabilidade
                '--chromium-arg=--disable-client-side-phishing-detection',     # Anti-detecção
                '--chromium-arg=--disable-component-update',                    # Sem atualizações
                '--chromium-arg=--disable-domain-reliability',                  # Anti-detecção
                '--chromium-arg=--disable-sync',                                # Sem sincronização
                '--chromium-arg=--disable-background-networking',               # Anti-detecção
                '--chromium-arg=--disable-breakpad',                            # Sem relatórios de crash
                '--chromium-arg=--disable-component-extensions-with-background-pages',  # Performance
                '--chromium-arg=--disable-back-forward-cache',                  # Compatibilidade
                '--chromium-arg=--disable-permissions-api',                     # Anti-detecção
                '--chromium-arg=--disable-web-security',                        # Flexibilidade
                '--chromium-arg=--allow-running-insecure-content',              # Compatibilidade
                '--chromium-arg=--ignore-certificate-errors',                   # Compatibilidade
                '--chromium-arg=--ignore-ssl-errors',                           # Compatibilidade
                '--chromium-arg=--ignore-certificate-errors-spki-list',         # Compatibilidade
                '--chromium-arg=--ignore-certificate-errors-ssl-errors',        # Compatibilidade
                '--chromium-arg=--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',  # User-Agent realista
                '-v', '-s', '--tb=short',
                script_filename
            ]
            
            self.log_message(f"Comando: {' '.join(cmd)}")
            
            # Executar em subprocess com output em tempo real
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=os.getcwd()
            )
            
            self.log_message("✅ Chrome deve ter aberto! Processando...")
            
            # Ler output em tempo real
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.log_message(output.strip())
            
            # Aguardar conclusão
            process.wait()
            
            if process.returncode == 0:
                self.status_label.config(text="✅ Processamento concluído!")
                self.log_message("🎉 Processamento concluído com sucesso!")
                messagebox.showinfo("Sucesso", "Processamento concluído! Verifique os arquivos de resultado.")
            else:
                self.status_label.config(text="⚠️ Processamento com problemas")
                self.log_message("⚠️ Processamento finalizado com alguns problemas")
                messagebox.showwarning("Aviso", "Processamento finalizado. Verifique os logs e resultados.")
            
            # Limpar arquivo temporário
            try:
                os.remove(script_filename)
            except:
                pass
                
        except Exception as e:
            logging.error(f"Erro: {e}")
            logging.error(traceback.format_exc())
            self.status_label.config(text="❌ Erro")
            self.log_message(f"❌ Erro: {str(e)}")
            messagebox.showerror("Erro", f"Erro: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()
