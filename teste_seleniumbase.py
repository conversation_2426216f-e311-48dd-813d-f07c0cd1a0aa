#!/usr/bin/env python3
"""
Teste do SeleniumBase UC Mode para verificar se está funcionando
"""

import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_seleniumbase_uc_mode():
    """Testa o SeleniumBase UC Mode"""
    print("🔧 TESTE DO SELENIUMBASE UC MODE")
    print("=" * 50)
    
    try:
        from seleniumbase import SB
        print("✅ SeleniumBase importado com sucesso!")
        
        print("\n🚀 Testando UC Mode...")
        
        # Teste básico do UC Mode
        with SB(uc=True, test=True, headless=True) as sb:
            print("✅ UC Mode iniciado com sucesso!")
            
            # Testar navegação básica
            print("🌐 Testando navegação...")
            sb.open("https://httpbin.org/user-agent")
            
            # Verificar se conseguiu carregar
            print("✅ Página carregada com sucesso!")
            
            # Testar detecção de bot
            print("🛡️ Testando anti-detecção...")
            sb.open("https://bot.sannysoft.com/")
            
            print("✅ Teste de anti-detecção concluído!")
            
        print("\n" + "=" * 50)
        print("✅ SELENIUMBASE UC MODE FUNCIONANDO!")
        print("\nRecursos disponíveis:")
        print("- ✅ UC Mode (Undetected Chrome)")
        print("- ✅ Anti-detecção automática")
        print("- ✅ Bypass de sistemas anti-bot")
        print("- ✅ Navegação stealth")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Solução: pip install seleniumbase")
        return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def test_site_projudi():
    """Testa especificamente o site do PROJUDI"""
    print("\n🎯 TESTE ESPECÍFICO DO PROJUDI")
    print("=" * 50)
    
    try:
        from seleniumbase import SB
        
        print("🌐 Testando acesso ao PROJUDI...")
        
        with SB(uc=True, test=True, headless=True) as sb:
            # Tentar acessar a página de login do PROJUDI
            sb.open("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
            
            # Verificar se carregou sem erro de "acesso predatório"
            page_source = sb.get_page_source()
            
            if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                print("❌ Ainda detectando como acesso predatório")
                return False
            else:
                print("✅ Acesso ao PROJUDI bem-sucedido!")
                print("✅ Não foi detectado como acesso predatório!")
                return True
                
    except Exception as e:
        print(f"❌ Erro ao testar PROJUDI: {e}")
        return False

def main():
    """Função principal"""
    print("🔧 TESTE COMPLETO DO SELENIUMBASE")
    print("=" * 60)
    
    # Teste 1: UC Mode básico
    if not test_seleniumbase_uc_mode():
        print("\n❌ Falha no teste básico do SeleniumBase")
        return
    
    # Teste 2: Site específico PROJUDI
    if test_site_projudi():
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("\n✅ O SeleniumBase UC Mode está funcionando perfeitamente!")
        print("✅ Pronto para usar no programa principal!")
    else:
        print("\n⚠️ Teste do PROJUDI falhou")
        print("💡 Pode ser necessário ajustar configurações específicas")
    
    print("\n" + "=" * 60)
    print("📋 PRÓXIMOS PASSOS:")
    print("1. Execute: python buscaNomeCPF_seleniumbase.py")
    print("2. Use a planilha de exemplo: exemplo_processos.xlsx")
    print("3. Monitore os logs em: busca_nome_cpf.log")

if __name__ == "__main__":
    main()
