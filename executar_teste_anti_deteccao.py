"""
Script para executar rapidamente o teste de anti-detecção
"""

import subprocess
import sys
import os

def executar_teste():
    """Executa o teste de anti-detecção com todas as configurações"""
    
    print("🚀 EXECUTANDO TESTE DE ANTI-DETECÇÃO PROJUDI...")
    print("=" * 60)
    
    # Comando com todas as configurações de anti-detecção
    cmd = [
        sys.executable, '-m', 'pytest',
        '--uc',                    # UC Mode (undetected-chromedriver)
        '--uc-cdp-events',         # Capturar eventos CDP
        '--incognito',             # Modo incógnito
        '--disable-csp',           # Desabilitar Content Security Policy
        '--disable-ws',            # Desabilitar Web Security
        '--block-images',          # Bloquear imagens para performance
        '--chromium-arg=--disable-blink-features=AutomationControlled',
        '--chromium-arg=--disable-dev-shm-usage',
        '--chromium-arg=--no-first-run',
        '--chromium-arg=--disable-default-apps',
        '--chromium-arg=--disable-extensions-file-access-check',
        '--chromium-arg=--disable-background-timer-throttling',
        '--chromium-arg=--disable-backgrounding-occluded-windows',
        '--chromium-arg=--disable-renderer-backgrounding',
        '--chromium-arg=--disable-features=TranslateUI',
        '--chromium-arg=--disable-ipc-flooding-protection',
        '--chromium-arg=--disable-hang-monitor',
        '--chromium-arg=--disable-client-side-phishing-detection',
        '--chromium-arg=--disable-component-update',
        '--chromium-arg=--disable-domain-reliability',
        '--chromium-arg=--disable-sync',
        '--chromium-arg=--disable-background-networking',
        '--chromium-arg=--disable-breakpad',
        '--chromium-arg=--disable-component-extensions-with-background-pages',
        '--chromium-arg=--disable-back-forward-cache',
        '--chromium-arg=--disable-permissions-api',
        '--chromium-arg=--disable-web-security',
        '--chromium-arg=--allow-running-insecure-content',
        '--chromium-arg=--ignore-certificate-errors',
        '--chromium-arg=--ignore-ssl-errors',
        '--chromium-arg=--ignore-certificate-errors-spki-list',
        '--chromium-arg=--ignore-certificate-errors-ssl-errors',
        '--chromium-arg=--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        '-v', '-s', '--tb=short',
        'teste_anti_deteccao_projudi.py'
    ]
    
    print("📋 Comando a ser executado:")
    print(" ".join(cmd))
    print("=" * 60)
    
    try:
        # Executar o teste
        print("🔄 Iniciando teste...")
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n" + "=" * 60)
            print("✅ TESTE CONCLUÍDO COM SUCESSO!")
            print("✅ Configurações de anti-detecção estão funcionando!")
            print("✅ Você pode usar o programa principal agora!")
        else:
            print("\n" + "=" * 60)
            print("⚠️ TESTE CONCLUÍDO COM AVISOS")
            print("⚠️ Verifique os logs acima para detalhes")
            print("⚠️ Pode ser necessário ajustar as configurações")
            
    except Exception as e:
        print(f"\n❌ ERRO AO EXECUTAR TESTE: {e}")
        print("💡 Certifique-se de que o SeleniumBase está instalado:")
        print("   pip install seleniumbase")

def main():
    """Função principal"""
    
    print("🧪 TESTE DE ANTI-DETECÇÃO PARA PROJUDI")
    print("=" * 60)
    print("Este teste verificará se as configurações conseguem")
    print("contornar a detecção de 'acesso predatório' no PROJUDI")
    print("=" * 60)
    
    # Verificar se os arquivos necessários existem
    arquivos_necessarios = [
        'teste_anti_deteccao_projudi.py',
        'anti_deteccao_config.py'
    ]
    
    for arquivo in arquivos_necessarios:
        if not os.path.exists(arquivo):
            print(f"❌ ERRO: Arquivo {arquivo} não encontrado!")
            return
    
    print("✅ Todos os arquivos necessários encontrados!")
    
    # Perguntar se o usuário quer continuar
    resposta = input("\n🤔 Deseja executar o teste? (s/n): ").lower().strip()
    
    if resposta in ['s', 'sim', 'y', 'yes']:
        executar_teste()
    else:
        print("❌ Teste cancelado pelo usuário")

if __name__ == "__main__":
    main()
