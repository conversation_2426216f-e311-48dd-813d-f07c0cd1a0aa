"""
Teste específico para verificar se as configurações de anti-detecção
conseguem contornar a detecção de "acesso predatório" no PROJUDI
"""

from seleniumbase import BaseCase
import time
import random
from anti_deteccao_config import *

class TestAntiDeteccaoProjudi(BaseCase):

    def test_acesso_projudi_sem_deteccao(self):
        """Testa se consegue acessar o PROJUDI sem ser detectado como acesso predatório"""
        
        print("🚀 INICIANDO TESTE DE ANTI-DETECÇÃO PROJUDI...")
        
        try:
            # Aplicar configurações de stealth
            print("🛡️ Aplicando configurações avançadas de stealth...")
            
            # Executar scripts de stealth
            for script_name, script_code in STEALTH_SCRIPTS.items():
                try:
                    self.execute_script(script_code)
                    print(f"✅ {script_name} aplicado")
                except Exception as e:
                    print(f"⚠️ Erro em {script_name}: {e}")
            
            # Teste 1: Acesso inicial à página de login
            print("\n📋 TESTE 1: Acessando página de login...")
            
            for tentativa in range(3):
                try:
                    print(f"🔄 Tentativa {tentativa + 1}/3...")
                    
                    # Delay aleatório
                    delay = get_human_delay("after_page_load")
                    print(f"⏳ Aguardando {delay:.1f}s...")
                    self.sleep(delay)
                    
                    # Acessar página
                    self.uc_open_with_reconnect(PROJUDI_CONFIG["login_url"], reconnect_time=8)
                    
                    # Aguardar carregamento
                    self.sleep(get_human_delay("page_load_wait"))
                    
                    # Verificar se carregou
                    page_source = self.get_page_source()
                    
                    if is_predatory_access_detected(page_source):
                        print(f"❌ TENTATIVA {tentativa + 1}: ACESSO PREDATÓRIO DETECTADO!")
                        if tentativa < 2:
                            delay_retry = get_human_delay("predatory_detection_wait")
                            print(f"⏳ Aguardando {delay_retry:.1f}s antes da próxima tentativa...")
                            self.sleep(delay_retry)
                        continue
                    else:
                        print(f"✅ TENTATIVA {tentativa + 1}: Página de login acessada sem detecção!")
                        break
                        
                except Exception as e:
                    print(f"❌ Erro na tentativa {tentativa + 1}: {e}")
                    if tentativa == 2:
                        raise Exception("Falha em todas as tentativas de acesso")
            
            # Teste 2: Verificar se campos de login estão presentes
            print("\n📋 TESTE 2: Verificando campos de login...")
            
            if self.is_element_present('//*[@id="login"]', timeout=10):
                print("✅ Campo de usuário encontrado!")
            else:
                print("❌ Campo de usuário NÃO encontrado!")
                return False
                
            if self.is_element_present('//*[@id="senha"]', timeout=5):
                print("✅ Campo de senha encontrado!")
            else:
                print("❌ Campo de senha NÃO encontrado!")
                return False
            
            # Teste 3: Simular preenchimento dos campos (sem fazer login real)
            print("\n📋 TESTE 3: Simulando preenchimento de campos...")
            
            # Simular digitação no campo usuário
            self.clear('//*[@id="login"]')
            test_user = "teste123"
            for char in test_user:
                self.type('//*[@id="login"]', char)
                self.sleep(get_human_delay("typing_min"))
            
            print("✅ Campo usuário preenchido com simulação humana!")
            
            # Delay entre campos
            self.sleep(get_human_delay("between_fields"))
            
            # Simular digitação no campo senha
            self.clear('//*[@id="senha"]')
            test_pass = "teste456"
            for char in test_pass:
                self.type('//*[@id="senha"]', char)
                self.sleep(get_human_delay("typing_min"))
            
            print("✅ Campo senha preenchido com simulação humana!")
            
            # Teste 4: Acessar página de busca diretamente
            print("\n📋 TESTE 4: Testando acesso à página de busca...")
            
            delay = get_human_delay("between_processes")
            print(f"⏳ Aguardando {delay:.1f}s...")
            self.sleep(delay)
            
            try:
                self.uc_open_with_reconnect(PROJUDI_CONFIG["search_url"], reconnect_time=5)
                self.sleep(get_human_delay("page_load_wait"))
                
                page_source = self.get_page_source()
                
                if is_predatory_access_detected(page_source):
                    print("❌ ACESSO PREDATÓRIO DETECTADO na página de busca!")
                    return False
                else:
                    print("✅ Página de busca acessada sem detecção!")
                    
                    # Verificar se campo de busca está presente
                    if self.is_element_present('//*[@id="ProcessoNumero"]', timeout=8):
                        print("✅ Campo de busca de processo encontrado!")
                    else:
                        print("⚠️ Campo de busca não encontrado (pode precisar de login)")
                        
            except Exception as e:
                print(f"⚠️ Erro ao acessar página de busca: {e}")
            
            # Teste 5: Verificar detecção em múltiplos acessos
            print("\n📋 TESTE 5: Testando múltiplos acessos consecutivos...")
            
            for i in range(3):
                print(f"🔄 Acesso {i + 1}/3...")
                
                # Delay aleatório entre acessos
                delay = get_human_delay("between_processes")
                self.sleep(delay)
                
                try:
                    self.uc_open_with_reconnect(PROJUDI_CONFIG["login_url"], reconnect_time=3)
                    self.sleep(2)
                    
                    page_source = self.get_page_source()
                    
                    if is_predatory_access_detected(page_source):
                        print(f"❌ ACESSO {i + 1}: PREDATÓRIO DETECTADO!")
                        return False
                    else:
                        print(f"✅ ACESSO {i + 1}: Sem detecção!")
                        
                except Exception as e:
                    print(f"⚠️ Erro no acesso {i + 1}: {e}")
            
            print("\n🎉 TODOS OS TESTES PASSARAM!")
            print("✅ Configurações de anti-detecção estão funcionando!")
            print("✅ Pronto para usar no programa principal!")
            
            return True
            
        except Exception as e:
            print(f"\n❌ ERRO CRÍTICO NO TESTE: {e}")
            return False

    def test_deteccao_basica_bot(self):
        """Testa detecção básica de bot em sites de teste"""
        
        print("\n🛡️ TESTE ADICIONAL: Verificando detecção básica de bot...")
        
        try:
            # Testar em site de detecção de bot
            self.uc_open_with_reconnect("https://bot.sannysoft.com/", reconnect_time=3)
            self.sleep(3)
            
            # Verificar propriedade webdriver
            webdriver_result = self.execute_script("return navigator.webdriver")
            
            if webdriver_result is None or webdriver_result == False:
                print("✅ Propriedade webdriver oculta com sucesso!")
            else:
                print("⚠️ Propriedade webdriver ainda visível")
            
            # Verificar plugins
            plugins_count = self.execute_script("return navigator.plugins.length")
            print(f"📊 Plugins detectados: {plugins_count}")
            
            # Verificar languages
            languages = self.execute_script("return navigator.languages")
            print(f"🌐 Languages: {languages}")
            
            print("✅ Teste de detecção básica concluído!")
            
        except Exception as e:
            print(f"⚠️ Erro no teste de detecção básica: {e}")

if __name__ == "__main__":
    print("🧪 Para executar este teste, use:")
    print("python -m pytest --uc --uc-cdp-events --incognito --disable-csp --disable-ws -v -s teste_anti_deteccao_projudi.py")
