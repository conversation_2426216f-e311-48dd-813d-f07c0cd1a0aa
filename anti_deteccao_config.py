"""
Configurações avançadas de anti-detecção para SeleniumBase UC Mode
Especificamente otimizado para contornar sistemas de detecção de acesso predatório
"""

# User-Agents realistas e atualizados
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
]

# Argumentos Chrome para máxima anti-detecção
CHROME_ARGS_STEALTH = [
    "--disable-blink-features=AutomationControlled",
    "--disable-dev-shm-usage",
    "--no-first-run",
    "--disable-default-apps",
    "--disable-extensions-file-access-check",
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-features=TranslateUI",
    "--disable-ipc-flooding-protection",
    "--disable-hang-monitor",
    "--disable-client-side-phishing-detection",
    "--disable-component-update",
    "--disable-domain-reliability",
    "--disable-sync",
    "--disable-background-networking",
    "--disable-breakpad",
    "--disable-component-extensions-with-background-pages",
    "--disable-back-forward-cache",
    "--disable-permissions-api",
    "--disable-web-security",
    "--allow-running-insecure-content",
    "--ignore-certificate-errors",
    "--ignore-ssl-errors",
    "--ignore-certificate-errors-spki-list",
    "--ignore-certificate-errors-ssl-errors",
    "--disable-logging",
    "--disable-gpu-logging",
    "--silent",
    "--no-sandbox",
    "--disable-gpu",
    "--disable-software-rasterizer",
    "--disable-background-mode",
    "--disable-extensions",
    "--disable-plugins",
    "--disable-images",
    "--disable-javascript-harmony-shipping",
    "--disable-prompt-on-repost",
    "--disable-popup-blocking",
    "--disable-translate",
    "--disable-infobars",
    "--disable-notifications",
    "--disable-save-password-bubble",
    "--disable-single-click-autofill",
    "--disable-autofill-keyboard-accessory-view",
    "--disable-full-form-autofill-ios",
    "--disable-autofill",
    "--disable-password-generation",
    "--disable-password-manager-reauthentication"
]

# Scripts JavaScript para mascarar propriedades de detecção
STEALTH_SCRIPTS = {
    "webdriver_removal": """
        // Remover todas as propriedades relacionadas ao webdriver
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Remover chrome.runtime que indica automação
        delete window.chrome.runtime;
        
        // Mascarar window.chrome
        window.chrome = {
            runtime: {},
            loadTimes: function() {
                return {
                    commitLoadTime: Date.now() / 1000 - Math.random() * 100,
                    finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 50,
                    finishLoadTime: Date.now() / 1000 - Math.random() * 10,
                    firstPaintAfterLoadTime: 0,
                    firstPaintTime: Date.now() / 1000 - Math.random() * 20,
                    navigationType: "Other",
                    npnNegotiatedProtocol: "h2",
                    requestTime: Date.now() / 1000 - Math.random() * 200,
                    startLoadTime: Date.now() / 1000 - Math.random() * 300,
                    wasAlternateProtocolAvailable: false,
                    wasFetchedViaSpdy: true,
                    wasNpnNegotiated: true
                };
            },
            csi: function() {
                return {
                    pageT: Date.now() - Math.random() * 1000,
                    startE: Date.now() - Math.random() * 2000,
                    tran: Math.floor(Math.random() * 20)
                };
            }
        };
    """,
    
    "navigator_masking": """
        // Mascarar propriedades do navigator
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {
                    0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                    description: "Portable Document Format",
                    filename: "internal-pdf-viewer",
                    length: 1,
                    name: "Chrome PDF Plugin"
                },
                {
                    0: {type: "application/pdf", suffixes: "pdf", description: ""},
                    description: "",
                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    length: 1,
                    name: "Chrome PDF Viewer"
                }
            ],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['pt-BR', 'pt', 'en-US', 'en'],
        });
        
        Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
        });
        
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => 8,
        });
        
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => 8,
        });
    """,
    
    "permissions_masking": """
        // Mascarar API de permissões
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // Mascarar outras APIs que podem detectar automação
        if (window.outerHeight === 0) {
            Object.defineProperty(window, 'outerHeight', {
                get: () => window.innerHeight,
            });
        }
        
        if (window.outerWidth === 0) {
            Object.defineProperty(window, 'outerWidth', {
                get: () => window.innerWidth,
            });
        }
    """,
    
    "timing_randomization": """
        // Randomizar timings para parecer mais humano
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        window.setTimeout = function(callback, delay, ...args) {
            const randomDelay = delay + (Math.random() * 100 - 50);
            return originalSetTimeout.call(this, callback, Math.max(0, randomDelay), ...args);
        };
        
        window.setInterval = function(callback, delay, ...args) {
            const randomDelay = delay + (Math.random() * 100 - 50);
            return originalSetInterval.call(this, callback, Math.max(0, randomDelay), ...args);
        };
    """
}

# Configurações de delays para simular comportamento humano
HUMAN_DELAYS = {
    "typing_min": 0.05,
    "typing_max": 0.15,
    "between_fields": (0.5, 1.2),
    "before_click": (0.8, 1.5),
    "after_page_load": (2, 5),
    "between_processes": (2, 6),
    "search_result_wait": (4, 8),
    "predatory_detection_wait": (15, 30),
    "login_retry_wait": (3, 8),
    "page_load_wait": (2, 4)
}

# Headers HTTP adicionais para parecer mais realista
HTTP_HEADERS = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "DNT": "1",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Cache-Control": "max-age=0"
}

# Configurações específicas para o PROJUDI
PROJUDI_CONFIG = {
    "base_url": "https://projudi.tjgo.jus.br",
    "login_url": "https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200",
    "search_url": "https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24",
    "max_login_attempts": 5,
    "max_search_attempts": 3,
    "reconnect_time": 8,
    "page_timeout": 10,
    "element_timeout": 8
}

# Mensagens de detecção de acesso predatório (para identificar)
PREDATORY_ACCESS_INDICATORS = [
    "predatório",
    "predatory", 
    "acesso negado",
    "access denied",
    "bloqueado",
    "blocked",
    "suspeito",
    "suspicious",
    "robô",
    "robot",
    "bot",
    "automatizado",
    "automated"
]

def get_random_user_agent():
    """Retorna um User-Agent aleatório da lista"""
    import random
    return random.choice(USER_AGENTS)

def get_human_delay(delay_type):
    """Retorna um delay aleatório baseado no tipo"""
    import random
    if delay_type in HUMAN_DELAYS:
        delay_range = HUMAN_DELAYS[delay_type]
        if isinstance(delay_range, tuple):
            return random.uniform(delay_range[0], delay_range[1])
        else:
            return delay_range
    return random.uniform(0.5, 2.0)

def apply_stealth_scripts(driver):
    """Aplica todos os scripts de stealth ao driver"""
    for script_name, script_code in STEALTH_SCRIPTS.items():
        try:
            driver.execute_script(script_code)
            print(f"✅ Script {script_name} aplicado com sucesso")
        except Exception as e:
            print(f"⚠️ Erro ao aplicar script {script_name}: {e}")

def is_predatory_access_detected(page_source):
    """Verifica se há indicadores de detecção de acesso predatório"""
    page_lower = page_source.lower()
    for indicator in PREDATORY_ACCESS_INDICATORS:
        if indicator in page_lower:
            return True
    return False
