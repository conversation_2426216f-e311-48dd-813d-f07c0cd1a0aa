# 🛡️ Busca Nome e CPF - UC Mode ULTRA ROBUSTO

## 🚀 Melhorias Implementadas para Contornar Detecção de Acesso Predatório

### ✨ Principais Melhorias

#### 1. **Scripts JavaScript de Stealth Avançados**
- ✅ Remoção completa da propriedade `navigator.webdriver`
- ✅ Mascaramento de `window.chrome.runtime`
- ✅ Simulação realista de plugins do navegador
- ✅ Configuração de languages brasileiras (`pt-BR`, `pt`)
- ✅ Mascaramento da API de permissões
- ✅ Randomização de timings para parecer mais humano

#### 2. **Argumentos Chrome Ultra Robustos**
- ✅ `--disable-blink-features=AutomationControlled` (principal anti-detecção)
- ✅ Desabilitação de 30+ recursos que podem indicar automação
- ✅ User-Agent realista e atualizado
- ✅ Configurações de performance otimizadas
- ✅ Modo incógnito para evitar rastros

#### 3. **Simulação de Comportamento Humano**
- ✅ Digitação caractere por caractere com delays aleatórios
- ✅ Delays variáveis entre ações (0.05s a 30s dependendo da situação)
- ✅ Múltiplas tentativas com backoff exponencial
- ✅ Verificação inteligente de detecção de acesso predatório

#### 4. **Sistema de Retry Inteligente**
- ✅ 5 tentativas de login com delays crescentes
- ✅ 3 tentativas por página de busca
- ✅ Reconexão automática em caso de falha
- ✅ Salvamento a cada 3 processos para evitar perda de dados

#### 5. **Detecção e Tratamento de Acesso Predatório**
- ✅ Identificação automática de mensagens de bloqueio
- ✅ Delays de 15-30 segundos quando detectado
- ✅ Continuação do processamento após detecção
- ✅ Log detalhado de todos os eventos

### 📁 Arquivos Criados/Modificados

1. **`buscaNomeCPF_robusto.py`** - Programa principal com todas as melhorias
2. **`anti_deteccao_config.py`** - Configurações avançadas de anti-detecção
3. **`teste_anti_deteccao_projudi.py`** - Teste específico para o PROJUDI
4. **`executar_teste_anti_deteccao.py`** - Script para executar testes rapidamente

### 🧪 Como Testar as Melhorias

#### Opção 1: Teste Rápido
```bash
python executar_teste_anti_deteccao.py
```

#### Opção 2: Teste Manual
```bash
python -m pytest --uc --uc-cdp-events --incognito --disable-csp --disable-ws -v -s teste_anti_deteccao_projudi.py
```

### 🚀 Como Usar o Programa Principal

1. **Execute o programa:**
   ```bash
   python buscaNomeCPF_robusto.py
   ```

2. **Preencha os dados:**
   - Usuário e senha do PROJUDI
   - Selecione a planilha Excel (primeira coluna = números de processo)

3. **Clique em "Iniciar Busca"**

4. **Acompanhe o progresso:**
   - Logs detalhados em tempo real
   - Salvamento automático a cada 3 processos
   - Tratamento inteligente de erros

### 🛡️ Técnicas de Anti-Detecção Implementadas

#### JavaScript Stealth
```javascript
// Remove detecção de webdriver
Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
});

// Mascara chrome.runtime
delete window.chrome.runtime;

// Simula plugins reais
Object.defineProperty(navigator, 'plugins', {
    get: () => [/* plugins realistas */],
});
```

#### Argumentos Chrome
```bash
--disable-blink-features=AutomationControlled
--disable-dev-shm-usage
--no-first-run
--disable-default-apps
# ... mais 25+ argumentos específicos
```

#### Simulação Humana
```python
# Digitação com delays aleatórios
for char in texto:
    self.type(campo, char)
    self.sleep(random.uniform(0.05, 0.15))

# Delays entre ações
delay = random.uniform(2, 6)
self.sleep(delay)
```

### 📊 Resultados Esperados

- ✅ **Redução significativa** de detecções de "acesso predatório"
- ✅ **Maior estabilidade** no processamento de múltiplos processos
- ✅ **Logs mais detalhados** para acompanhamento
- ✅ **Salvamento automático** para evitar perda de dados
- ✅ **Tratamento robusto** de erros e reconexões

### ⚠️ Observações Importantes

1. **Primeira Execução**: Pode demorar mais para configurar o Chrome
2. **Delays Intencionais**: Os delays são propositais para evitar detecção
3. **Múltiplas Tentativas**: O sistema tenta várias vezes antes de desistir
4. **Salvamento Frequente**: Dados são salvos a cada 3 processos processados

### 🔧 Configurações Avançadas

Para ajustar as configurações, edite o arquivo `anti_deteccao_config.py`:

```python
# Ajustar delays
HUMAN_DELAYS = {
    "typing_min": 0.05,        # Velocidade de digitação
    "between_processes": (2, 6), # Delay entre processos
    "predatory_detection_wait": (15, 30)  # Delay após detecção
}

# Adicionar novos User-Agents
USER_AGENTS.append("Seu novo User-Agent aqui")
```

### 🆘 Solução de Problemas

#### Se ainda houver detecção:
1. Aumente os delays em `HUMAN_DELAYS`
2. Execute o teste primeiro: `python executar_teste_anti_deteccao.py`
3. Verifique se todos os argumentos Chrome estão sendo aplicados
4. Considere usar um proxy ou VPN

#### Se o Chrome não abrir:
1. Verifique se o SeleniumBase está atualizado: `pip install --upgrade seleniumbase`
2. Execute: `seleniumbase install chromedriver`
3. Tente executar o teste primeiro

### 📈 Monitoramento

O programa agora fornece logs muito mais detalhados:
- 🚀 Início de cada etapa
- ✅ Sucessos e confirmações
- ⚠️ Avisos e tentativas
- ❌ Erros com detalhes
- 💾 Salvamentos automáticos
- 📊 Estatísticas de progresso

### 🎯 Próximos Passos

Se ainda houver problemas, considere:
1. Implementar rotação de proxies
2. Adicionar mais variação nos User-Agents
3. Implementar captcha solving
4. Usar múltiplas sessões paralelas com delays

---

**🎉 Com essas melhorias, o sistema deve conseguir contornar a detecção de "acesso predatório" de forma muito mais eficaz!**
