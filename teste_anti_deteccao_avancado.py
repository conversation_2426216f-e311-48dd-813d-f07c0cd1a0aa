#!/usr/bin/env python3
"""
Teste avançado para verificar se conseguimos contornar a detecção de "acesso predatório"
"""

import logging
import time

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_uc_mode_projudi():
    """Testa UC Mode no site PROJUDI"""
    print("🔧 TESTE UC MODE - PROJUDI")
    print("=" * 50)
    
    try:
        from seleniumbase import SB
        
        print("🚀 Testando UC Mode com configurações anti-detecção...")
        
        with SB(uc=True, test=True, headless=False, incognito=True,
               disable_csp=True, disable_ws=True, block_images=True) as sb:
            
            print("✅ UC Mode iniciado!")
            
            # Testar acesso ao PROJUDI
            print("🌐 Acessando PROJUDI...")
            sb.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=3)
            
            # Aguardar carregamento
            sb.sleep(5)
            
            # Verificar se há mensagem de acesso predatório
            page_source = sb.get_page_source()
            
            if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                print("❌ AINDA DETECTANDO COMO ACESSO PREDATÓRIO")
                print("💡 Tentando métodos mais avançados...")
                return False
            else:
                print("✅ SUCESSO! Não foi detectado como acesso predatório!")
                print("✅ UC Mode funcionando corretamente!")
                return True
                
    except Exception as e:
        print(f"❌ Erro no teste UC Mode: {e}")
        return False

def test_cdp_mode_projudi():
    """Testa CDP Mode no site PROJUDI"""
    print("\n🔧 TESTE CDP MODE - PROJUDI")
    print("=" * 50)
    
    try:
        from seleniumbase import SB
        
        print("🚀 Testando CDP Mode com máxima proteção...")
        
        with SB(uc=True, test=True, headless=False, incognito=True,
               disable_csp=True, disable_ws=True, block_images=True,
               chromium_arg="--disable-blink-features=AutomationControlled") as sb:
            
            print("✅ CDP Mode iniciado!")
            
            # Testar acesso usando CDP
            print("🌐 Acessando PROJUDI com CDP...")
            sb.cdp.open("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
            
            # Aguardar carregamento
            sb.sleep(5)
            
            # Verificar se há mensagem de acesso predatório
            page_source = sb.get_page_source()
            
            if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                print("❌ AINDA DETECTANDO COMO ACESSO PREDATÓRIO")
                return False
            else:
                print("✅ SUCESSO! CDP Mode contornou a detecção!")
                print("✅ Pronto para usar no programa principal!")
                return True
                
    except Exception as e:
        print(f"❌ Erro no teste CDP Mode: {e}")
        return False

def test_multiple_methods():
    """Testa múltiplos métodos para encontrar o que funciona"""
    print("\n🔧 TESTE MÚLTIPLOS MÉTODOS")
    print("=" * 50)
    
    methods = [
        {
            "name": "UC Mode Básico",
            "config": {"uc": True, "test": True, "headless": False}
        },
        {
            "name": "UC Mode + Incognito",
            "config": {"uc": True, "test": True, "headless": False, "incognito": True}
        },
        {
            "name": "UC Mode + Todas as proteções",
            "config": {"uc": True, "test": True, "headless": False, "incognito": True,
                      "disable_csp": True, "disable_ws": True, "block_images": True}
        },
        {
            "name": "UC Mode + CDP + Máxima proteção",
            "config": {"uc": True, "test": True, "headless": False, "incognito": True,
                      "disable_csp": True, "disable_ws": True, "block_images": True,
                      "chromium_arg": "--disable-blink-features=AutomationControlled"}
        }
    ]
    
    try:
        from seleniumbase import SB
        
        for i, method in enumerate(methods, 1):
            print(f"\n🧪 Teste {i}: {method['name']}")
            print("-" * 30)
            
            try:
                with SB(**method['config']) as sb:
                    print(f"✅ {method['name']} iniciado")
                    
                    # Usar uc_open_with_reconnect se disponível
                    if hasattr(sb, 'uc_open_with_reconnect'):
                        sb.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=2)
                    else:
                        sb.open("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
                    
                    sb.sleep(3)
                    
                    page_source = sb.get_page_source()
                    
                    if "predatório" in page_source.lower():
                        print(f"❌ {method['name']}: Ainda detectado")
                    else:
                        print(f"✅ {method['name']}: SUCESSO!")
                        return method['name']
                        
            except Exception as e:
                print(f"❌ {method['name']}: Erro - {e}")
                
        return None
        
    except ImportError:
        print("❌ SeleniumBase não disponível")
        return None

def main():
    """Função principal"""
    print("🛡️ TESTE AVANÇADO ANTI-DETECÇÃO PROJUDI")
    print("=" * 60)
    
    # Teste 1: UC Mode
    uc_success = test_uc_mode_projudi()
    
    # Teste 2: CDP Mode
    cdp_success = test_cdp_mode_projudi()
    
    # Teste 3: Múltiplos métodos
    best_method = test_multiple_methods()
    
    print("\n" + "=" * 60)
    print("📊 RESULTADOS DOS TESTES:")
    print(f"UC Mode: {'✅ Sucesso' if uc_success else '❌ Falhou'}")
    print(f"CDP Mode: {'✅ Sucesso' if cdp_success else '❌ Falhou'}")
    print(f"Melhor método: {best_method if best_method else '❌ Nenhum funcionou'}")
    
    if uc_success or cdp_success or best_method:
        print("\n🎉 PELO MENOS UM MÉTODO FUNCIONOU!")
        print("\n📋 PRÓXIMOS PASSOS:")
        if cdp_success:
            print("1. Use: python buscaNomeCPF_cdp.py (CDP Mode)")
        elif uc_success:
            print("1. Use: python buscaNomeCPF_seleniumbase.py (UC Mode)")
        print("2. Monitore os logs para verificar se continua funcionando")
        print("3. Se voltar a detectar, execute este teste novamente")
    else:
        print("\n❌ TODOS OS MÉTODOS FALHARAM")
        print("\n💡 SOLUÇÕES ALTERNATIVAS:")
        print("1. Tente em horário diferente (madrugada)")
        print("2. Use conexão de internet diferente")
        print("3. Aguarde algumas horas antes de tentar novamente")
        print("4. Considere usar proxy/VPN")

if __name__ == "__main__":
    main()
