
from seleniumbase import BaseCase
import pandas as pd
from datetime import datetime
import os
import time
import random
import json

class TestBuscaProcessos(BaseCase):

    def test_buscar_processos(self):
        """Busca processos usando UC Mode ULTRA ROBUSTO com máxima anti-detecção"""

        print("🚀 INICIANDO SeleniumBase UC Mode ULTRA ROBUSTO...")

        # Lista de processos
        processos = ['5569451-19.2022.8.09.0051', '5880002-79.2024.8.09.0094', '5734543-80.2023.8.09.0094', '5427507-58.2024.8.09.0051', '5673565-72.2023.8.09.0051', '5054428-14.2022.8.09.0044', '5256825-13.2023.8.09.0049', '5752953-64.2022.8.09.0049', '5753781-60.2022.8.09.0049', '5813259-56.2023.8.09.0051', '5019948-23.2021.8.09.0051', '5259404-59.2022.8.09.0051', '5331085-64.2023.8.09.0145', '5666575-65.2023.8.09.0051', '5292461-16.2024.8.09.0175', '5040369-34.2021.8.09.0051', '5662215-87.2023.8.09.0051', '5505443-90.2021.8.09.0011', '5696710-64.2024.8.09.0006', '5714348-47.2023.8.09.0006', '5623712-40.2020.8.09.0006', '5673362-13.2023.8.09.0051', '5660383-19.2023.8.09.0051', '5655504-66.2023.8.09.0051', '5731283-84.2022.8.09.0011', ' \n5639664-16.2023.8.09.0051', '5596840-55.2020.8.09.0113', '5767410-25.2023.8.09.0160', '5506863-47.2023.8.09.0113', '5457450-97.2022.8.09.0146', '5826522-66.2023.8.09.0113', '5456959-58.2023.8.09.0113', '5231504-02.2024.8.09.0029', '5560589-88.2024.8.09.0051', '5045003-80.2024.8.09.0144', '5702748-29.2023.8.09.0006', '5040616-15.2021.8.09.0051', '5378398-75.2024.8.09.0051', '5177572-33.2024.8.09.0051', '5100359-23.2023.8.09.0006', '5775749-81.2022.8.09.0006', '5443449-08.2021.8.09.0158', '5492965-56.2023.8.09.0051', '5223431-66.2022.8.09.0011', '5164572-12.2023.8.09.0144', '5630323-63.2023.8.09.0051', '5553965-95.2023.8.09.0006', '5704674-07.2023.8.09.0051', ' \n5487045-38.2022.8.09.0051', '5106931-35.2015.8.09.0051', '5760985-41.2024.8.09.0032', '5121416-25.2024.8.09.0051', '5443029-28.2024.8.09.0051', '5648924-58.2023.8.09.0006', '5822290-41.2023.8.09.0006', '5127598-84.2023.8.09.0011', '5008737-82.2024.8.09.0051', '5334297-94.2017.8.09.0051', '5578604-18.2018.8.09.0051', '5009310-32.2022.8.09.0006', '5321223-65.2020.8.09.0051', '5789992-55.2023.8.09.0051', '5702996-92.2023.8.09.0006', '5467265-96.2021.8.09.0100', '5344056-72.2023.8.09.0051', ' \n5636862-50.2020.8.09.0051', '5606559-48.2023.8.09.0051', '5804290-56.2024.8.09.0006', '5388549-31.2021.8.09.0011', '5141277-40.2021.8.09.0006', '5728623-98.2023.8.09.0006', '5684817-72.2023.8.09.0051', '5475756-17.2022.8.09.0146', '5406975-97.2023.8.09.0051', '5247329-28.2021.8.09.0146', '5369994-35.2024.8.09.0051', '5177427-74.2024.8.09.0051', '5517872-24.2023.8.09.0010', '5517875-76.2023.8.09.0010', '5212969-56.2024.8.09.0051', '5740633-78.2019.8.09.0051', '5500282-34.2023.8.09.0010', '5605882-23.2020.8.09.0051', '5168842-33.2024.8.09.0051', '5099822-61.2022.8.09.0006', ' \n5580178-03.2023.8.09.0051', '5636316-87.2023.8.09.0051', '5260093-40.2021.8.09.0051', '5155418-21.2024.8.09.0051', '5578525-89.2023.8.09.0010', '5561484-12.2023.8.09.0010', '5750855-66.2023.8.09.0051', '5750069-28.2023.8.09.0146', '5661908-45.2021.8.09.0006', '5566166-81.2023.8.09.0051', '5740646-77.2019.8.09.0051', '5053190-70.2021.8.09.0051', '5607904-43.2021.8.09.0011', '5475793-09.2020.8.09.0051', '5330725-23.2023.8.09.0051', '5792947-59.2023.8.09.0051', '5203977-81.2023.8.09.0006', ' \n5498935-55.2022.8.09.0024', '5455399-73.2023.8.09.0051', '5461603-70.2022.8.09.0051', '5188401-73.2024.8.09.0051', '5388743-03.2024.8.09.0051', '5245430-81.2024.8.09.0051', '5016106-30.2024.8.09.0051', '5063254-42.2021.8.09.0051', '5008251-68.2022.8.09.0051', '5314419-18.2019.8.09.0051', '5306778-45.2021.8.09.0168', ' \n5193024-83.2024.8.09.0051', '5191158-11.2022.8.09.0051', '5694860-68.2023.8.09.0051', '5071213-59.2024.8.09.0051', '5110547-20.2021.8.09.0144', '5547952-51.2021.8.09.0006', '5824387-73.2023.8.09.0051', '5722225-38.2023.8.09.0006', '5610549-52.2020.8.09.0051', '5555799-03.2020.8.09.0051', '5186996-07.2021.8.09.0051', '5368667-55.2024.8.09.0051', '5169386-59.2024.8.09.0006', '5720333-31.2022.8.09.0006', '5562962-29.2023.8.09.0051', '5081622-02.2021.8.09.0051', '5613095-80.2020.8.09.0051', '5704831-77.2023.8.09.0051', ' \n5094106-83.2020.8.09.0051', '5616146-02.2020.8.09.0051', '5548632-43.2020.8.09.0112', '5754187-63.2024.8.09.0097', '5378844-71.2023.8.09.0097', '5103780-17.2022.8.09.0051', '5434811-50.2020.8.09.0051', '5647417-87.2024.8.09.0051', '5661355-86.2023.8.09.0051', '5529638-48.2023.8.09.0051', '5563445-30.2021.8.09.0051', '5577841-41.2023.8.09.0051', '5398936-08.2021.8.09.0011', '5740278-29.2023.8.09.0051', '5238083-94.2024.8.09.0051', '5292555-50.2021.8.09.0051', ' \n5140415-65.2020.8.09.0051']
        resultados = []

        try:
            # Configurações avançadas de stealth
            print("🛡️ Aplicando configurações avançadas de anti-detecção...")

            # Executar scripts de stealth avançados
            self.execute_script("""
                // Remover propriedades de detecção de webdriver
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // Mascarar plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                // Mascarar languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['pt-BR', 'pt', 'en-US', 'en'],
                });

                // Remover chrome runtime
                window.chrome = {
                    runtime: {}
                };

                // Mascarar permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)

            print("✅ Scripts de stealth aplicados!")

            # Login com retry e delays aleatórios
            print("🔐 FAZENDO LOGIN COM ANTI-DETECÇÃO...")
            login_sucesso = False

            for tentativa in range(5):  # Aumentado para 5 tentativas
                try:
                    print(f"🔄 Tentativa de login {tentativa + 1}/5...")

                    # Delay aleatório entre tentativas
                    if tentativa > 0:
                        delay = random.uniform(3, 8)
                        print(f"⏳ Aguardando {delay:.1f}s antes da próxima tentativa...")
                        self.sleep(delay)

                    # Abrir página com reconexão robusta
                    self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=8)

                    # Delay aleatório após carregamento
                    delay_inicial = random.uniform(2, 5)
                    print(f"⏳ Aguardando carregamento: {delay_inicial:.1f}s...")
                    self.sleep(delay_inicial)

                    # Verificar se a página carregou corretamente
                    if not self.is_element_present('//*[@id="login"]', timeout=10):
                        print(f"⚠️ Tentativa {tentativa + 1}: Campo de login não encontrado")
                        continue

                    # Simular comportamento humano no preenchimento
                    print("👤 Simulando digitação humana...")

                    # Limpar campo e digitar usuário com delays
                    self.clear('//*[@id="login"]')
                    for char in "07228313151":
                        self.type('//*[@id="login"]', char)
                        self.sleep(random.uniform(0.05, 0.15))

                    # Delay entre campos
                    self.sleep(random.uniform(0.5, 1.2))

                    # Limpar campo e digitar senha com delays
                    self.clear('//*[@id="senha"]')
                    for char in "Senhaprojudi24.":
                        self.type('//*[@id="senha"]', char)
                        self.sleep(random.uniform(0.05, 0.15))

                    # Delay antes de clicar
                    self.sleep(random.uniform(0.8, 1.5))

                    # Clicar no botão de login
                    self.click('//*[@id="formLogin"]/div[4]/input[1]')

                    # Aguardar processamento do login
                    delay_login = random.uniform(4, 8)
                    print(f"⏳ Aguardando processamento do login: {delay_login:.1f}s...")
                    self.sleep(delay_login)

                    # Verificar se o login foi bem-sucedido
                    if self.is_element_present('//*[@id="menuPrinciapl"]/ul[2]/li', timeout=10):
                        print("✅ LOGIN REALIZADO COM SUCESSO!")
                        login_sucesso = True
                        break
                    else:
                        print(f"❌ Tentativa {tentativa + 1}: Login falhou - verificando página...")

                        # Verificar se há mensagem de erro específica
                        page_source = self.get_page_source()
                        if "predatório" in page_source.lower():
                            print("🚨 ACESSO PREDATÓRIO DETECTADO NO LOGIN!")
                            # Aguardar mais tempo antes da próxima tentativa
                            self.sleep(random.uniform(10, 20))

                except Exception as e:
                    print(f"❌ Tentativa {tentativa + 1} falhou: {e}")
                    if tentativa == 4:  # Última tentativa
                        raise Exception("❌ FALHA CRÍTICA: Login falhou após 5 tentativas")

            if not login_sucesso:
                raise Exception("❌ FALHA CRÍTICA: Não foi possível fazer login")

            # Processar cada processo com máxima proteção
            print(f"📋 INICIANDO PROCESSAMENTO DE {len(processos)} PROCESSOS...")

            for i, processo in enumerate(processos, 1):
                print(f"\n🔍 PROCESSANDO {i}/{len(processos)}: {processo}")

                resultado = {
                    'Número do Processo': processo,
                    'Processo Encontrado': 'Não',
                    'Nome Polo Ativo': '',
                    'CPF Polo Ativo': ''
                }

                try:
                    # Verificar integridade do browser
                    try:
                        current_handle = self.driver.current_window_handle
                        print("✅ Browser ativo e funcionando")
                    except:
                        print("🚨 ERRO: Browser foi fechado! Tentando reconectar...")
                        self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=5)
                        resultado['Processo Encontrado'] = 'Erro - Browser Fechado'
                        resultados.append(resultado)
                        continue

                    # Delay aleatório entre processos para evitar detecção
                    if i > 1:
                        delay_entre_processos = random.uniform(2, 6)
                        print(f"⏳ Delay anti-detecção: {delay_entre_processos:.1f}s...")
                        self.sleep(delay_entre_processos)

                    # Navegar para busca com retry robusto
                    busca_carregada = False
                    for tentativa_busca in range(3):
                        try:
                            print(f"🌐 Carregando página de busca (tentativa {tentativa_busca + 1}/3)...")
                            self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24", reconnect_time=5)

                            # Aguardar carregamento
                            self.sleep(random.uniform(2, 4))

                            if self.is_element_present('//*[@id="ProcessoNumero"]', timeout=8):
                                print("✅ Página de busca carregada!")
                                busca_carregada = True
                                break
                            else:
                                print(f"⚠️ Tentativa {tentativa_busca + 1}: Campo de busca não encontrado")

                        except Exception as e:
                            print(f"❌ Tentativa {tentativa_busca + 1} falhou: {e}")
                            if tentativa_busca == 2:
                                print(f"🚨 ERRO CRÍTICO: Falha ao carregar página de busca para {processo}")
                                resultado['Processo Encontrado'] = 'Erro - Página não carregou'
                                break

                    if not busca_carregada:
                        resultados.append(resultado)
                        continue

                    # Limpar campo e inserir número do processo com simulação humana
                    print("📝 Inserindo número do processo...")
                    self.clear('//*[@id="ProcessoNumero"]')
                    self.sleep(random.uniform(0.3, 0.8))

                    # Digitar processo caractere por caractere
                    for char in str(processo):
                        self.type('//*[@id="ProcessoNumero"]', char)
                        self.sleep(random.uniform(0.08, 0.2))

                    self.sleep(random.uniform(0.5, 1.2))

                    # Tentar clicar em "mostrar todos os processos" se disponível
                    try:
                        if self.is_element_present('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]', timeout=3):
                            print("🔘 Clicando em 'Mostrar todos os processos'...")
                            self.click('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]')
                            self.sleep(random.uniform(0.5, 1.0))
                    except:
                        print("ℹ️ Botão 'Mostrar todos' não encontrado ou não clicável")

                    # Clicar em pesquisar
                    print("🔍 Executando pesquisa...")
                    self.click('//*[@id="divBotoesCentralizados"]/input[1]')

                    # Aguardar resultado com timeout maior
                    delay_pesquisa = random.uniform(4, 8)
                    print(f"⏳ Aguardando resultado: {delay_pesquisa:.1f}s...")
                    self.sleep(delay_pesquisa)

                    # Verificar resultado da pesquisa
                    print("📊 Analisando resultado...")
                    try:
                        page_source = self.get_page_source()

                        # Verificar se há mensagem de acesso predatório
                        if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                            print(f"🚨 PROCESSO {processo}: ACESSO PREDATÓRIO DETECTADO!")
                            resultado['Processo Encontrado'] = 'Erro - Acesso Predatório'

                            # Aguardar mais tempo quando detectado
                            delay_predatorio = random.uniform(15, 30)
                            print(f"⏳ Aguardando {delay_predatorio:.1f}s devido à detecção...")
                            self.sleep(delay_predatorio)

                        elif "Polo Ativo" in page_source or self.is_text_visible("Polo Ativo"):
                            resultado['Processo Encontrado'] = 'Sim'
                            print(f"✅ PROCESSO {processo}: ENCONTRADO!")

                            # Extrair dados com mais robustez
                            try:
                                print("📋 Extraindo dados do processo...")
                                spans = self.find_elements("span")
                                tds = self.find_elements("td")

                                # Combinar elementos para busca
                                elementos = spans + tds

                                for elemento in elementos:
                                    try:
                                        text = elemento.text.strip()
                                        if text and len(text) > 3:
                                            # Buscar nome (sem números no início)
                                            if not resultado['Nome Polo Ativo'] and not any(char.isdigit() for char in text[:3]) and len(text) > 5:
                                                # Filtrar textos que parecem ser nomes
                                                if not any(palavra in text.lower() for palavra in ['processo', 'número', 'data', 'valor', 'status']):
                                                    resultado['Nome Polo Ativo'] = text[:100]  # Limitar tamanho

                                            # Buscar CPF (formato XXX.XXX.XXX-XX)
                                            elif "." in text and "-" in text and len(text) >= 11 and len(text) <= 14:
                                                if text.count('.') == 2 and text.count('-') == 1:
                                                    resultado['CPF Polo Ativo'] = text
                                    except:
                                        continue

                                print(f"📋 Nome extraído: {resultado['Nome Polo Ativo'][:50] if resultado['Nome Polo Ativo'] else 'Não encontrado'}")
                                print(f"📋 CPF extraído: {resultado['CPF Polo Ativo'] if resultado['CPF Polo Ativo'] else 'Não encontrado'}")

                            except Exception as e:
                                print(f"⚠️ Erro na extração de dados: {e}")

                        else:
                            print(f"ℹ️ PROCESSO {processo}: NÃO ENCONTRADO")
                            resultado['Processo Encontrado'] = 'Não'

                    except Exception as e:
                        print(f"❌ Erro ao verificar resultado para {processo}: {e}")
                        resultado['Processo Encontrado'] = 'Erro - Falha na verificação'

                except Exception as e:
                    print(f"❌ ERRO GERAL no processo {processo}: {e}")
                    resultado['Processo Encontrado'] = 'Erro - Falha geral'

                resultados.append(resultado)
                print(f"✅ Processo {processo} processado: {resultado['Processo Encontrado']}")

                # Salvar a cada 3 processos para evitar perda de dados
                if i % 3 == 0:
                    print(f"💾 Salvamento intermediário ({i} processos)...")
                    self.salvar_resultados(resultados)

            # Salvar resultados finais
            print("💾 Salvando resultados finais...")
            self.salvar_resultados(resultados)
            print("🎉 PROCESSAMENTO CONCLUÍDO COM SUCESSO!")

        except Exception as e:
            print(f"🚨 ERRO CRÍTICO: {e}")
            if resultados:
                print("💾 Salvando resultados parciais...")
                self.salvar_resultados(resultados)

    def salvar_resultados(self, resultados):
        """Salva os resultados em Excel com tratamento de erro"""
        try:
            df_resultado = pd.DataFrame(resultados)
            nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            df_resultado.to_excel(nome_arquivo, index=False)
            print(f"💾 SALVANDO: Resultados salvos em: {nome_arquivo}")
            print(f"📊 Total de {len(resultados)} processos salvos")
        except Exception as e:
            print(f"❌ ERRO ao salvar: {e}")
            # Tentar salvar como CSV em caso de erro
            try:
                nome_csv = f"Resultados_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df_resultado.to_csv(nome_csv, index=False, encoding='utf-8-sig')
                print(f"💾 BACKUP: Resultados salvos como CSV: {nome_csv}")
            except:
                print("❌ FALHA CRÍTICA: Não foi possível salvar os resultados")
