
from seleniumbase import BaseCase
import pandas as pd
from datetime import datetime
import os
import time

class TestBuscaProcessos(BaseCase):

    def test_buscar_processos(self):
        """Busca processos usando UC Mode com tratamento robusto de erros"""

        print("INICIANDO SeleniumBase UC Mode...")

        # Lista de processos
        processos = ['5569451-19.2022.8.09.0051', '5880002-79.2024.8.09.0094', '5734543-80.2023.8.09.0094', '5427507-58.2024.8.09.0051', '5673565-72.2023.8.09.0051', '5054428-14.2022.8.09.0044', '5256825-13.2023.8.09.0049', '5752953-64.2022.8.09.0049', '5753781-60.2022.8.09.0049', '5813259-56.2023.8.09.0051', '5019948-23.2021.8.09.0051', '5259404-59.2022.8.09.0051', '5331085-64.2023.8.09.0145', '5666575-65.2023.8.09.0051', '5292461-16.2024.8.09.0175', '5040369-34.2021.8.09.0051', '5662215-87.2023.8.09.0051', '5505443-90.2021.8.09.0011', '5696710-64.2024.8.09.0006', '5714348-47.2023.8.09.0006', '5623712-40.2020.8.09.0006', '5673362-13.2023.8.09.0051', '5660383-19.2023.8.09.0051', '5655504-66.2023.8.09.0051', '5731283-84.2022.8.09.0011', ' \n5639664-16.2023.8.09.0051', '5596840-55.2020.8.09.0113', '5767410-25.2023.8.09.0160', '5506863-47.2023.8.09.0113', '5457450-97.2022.8.09.0146', '5826522-66.2023.8.09.0113', '5456959-58.2023.8.09.0113', '5231504-02.2024.8.09.0029', '5560589-88.2024.8.09.0051', '5045003-80.2024.8.09.0144', '5702748-29.2023.8.09.0006', '5040616-15.2021.8.09.0051', '5378398-75.2024.8.09.0051', '5177572-33.2024.8.09.0051', '5100359-23.2023.8.09.0006', '5775749-81.2022.8.09.0006', '5443449-08.2021.8.09.0158', '5492965-56.2023.8.09.0051', '5223431-66.2022.8.09.0011', '5164572-12.2023.8.09.0144', '5630323-63.2023.8.09.0051', '5553965-95.2023.8.09.0006', '5704674-07.2023.8.09.0051', ' \n5487045-38.2022.8.09.0051', '5106931-35.2015.8.09.0051', '5760985-41.2024.8.09.0032', '5121416-25.2024.8.09.0051', '5443029-28.2024.8.09.0051', '5648924-58.2023.8.09.0006', '5822290-41.2023.8.09.0006', '5127598-84.2023.8.09.0011', '5008737-82.2024.8.09.0051', '5334297-94.2017.8.09.0051', '5578604-18.2018.8.09.0051', '5009310-32.2022.8.09.0006', '5321223-65.2020.8.09.0051', '5789992-55.2023.8.09.0051', '5702996-92.2023.8.09.0006', '5467265-96.2021.8.09.0100', '5344056-72.2023.8.09.0051', ' \n5636862-50.2020.8.09.0051', '5606559-48.2023.8.09.0051', '5804290-56.2024.8.09.0006', '5388549-31.2021.8.09.0011', '5141277-40.2021.8.09.0006', '5728623-98.2023.8.09.0006', '5684817-72.2023.8.09.0051', '5475756-17.2022.8.09.0146', '5406975-97.2023.8.09.0051', '5247329-28.2021.8.09.0146', '5369994-35.2024.8.09.0051', '5177427-74.2024.8.09.0051', '5517872-24.2023.8.09.0010', '5517875-76.2023.8.09.0010', '5212969-56.2024.8.09.0051', '5740633-78.2019.8.09.0051', '5500282-34.2023.8.09.0010', '5605882-23.2020.8.09.0051', '5168842-33.2024.8.09.0051', '5099822-61.2022.8.09.0006', ' \n5580178-03.2023.8.09.0051', '5636316-87.2023.8.09.0051', '5260093-40.2021.8.09.0051', '5155418-21.2024.8.09.0051', '5578525-89.2023.8.09.0010', '5561484-12.2023.8.09.0010', '5750855-66.2023.8.09.0051', '5750069-28.2023.8.09.0146', '5661908-45.2021.8.09.0006', '5566166-81.2023.8.09.0051', '5740646-77.2019.8.09.0051', '5053190-70.2021.8.09.0051', '5607904-43.2021.8.09.0011', '5475793-09.2020.8.09.0051', '5330725-23.2023.8.09.0051', '5792947-59.2023.8.09.0051', '5203977-81.2023.8.09.0006', ' \n5498935-55.2022.8.09.0024', '5455399-73.2023.8.09.0051', '5461603-70.2022.8.09.0051', '5188401-73.2024.8.09.0051', '5388743-03.2024.8.09.0051', '5245430-81.2024.8.09.0051', '5016106-30.2024.8.09.0051', '5063254-42.2021.8.09.0051', '5008251-68.2022.8.09.0051', '5314419-18.2019.8.09.0051', '5306778-45.2021.8.09.0168', ' \n5193024-83.2024.8.09.0051', '5191158-11.2022.8.09.0051', '5694860-68.2023.8.09.0051', '5071213-59.2024.8.09.0051', '5110547-20.2021.8.09.0144', '5547952-51.2021.8.09.0006', '5824387-73.2023.8.09.0051', '5722225-38.2023.8.09.0006', '5610549-52.2020.8.09.0051', '5555799-03.2020.8.09.0051', '5186996-07.2021.8.09.0051', '5368667-55.2024.8.09.0051', '5169386-59.2024.8.09.0006', '5720333-31.2022.8.09.0006', '5562962-29.2023.8.09.0051', '5081622-02.2021.8.09.0051', '5613095-80.2020.8.09.0051', '5704831-77.2023.8.09.0051', ' \n5094106-83.2020.8.09.0051', '5616146-02.2020.8.09.0051', '5548632-43.2020.8.09.0112', '5754187-63.2024.8.09.0097', '5378844-71.2023.8.09.0097', '5103780-17.2022.8.09.0051', '5434811-50.2020.8.09.0051', '5647417-87.2024.8.09.0051', '5661355-86.2023.8.09.0051', '5529638-48.2023.8.09.0051', '5563445-30.2021.8.09.0051', '5577841-41.2023.8.09.0051', '5398936-08.2021.8.09.0011', '5740278-29.2023.8.09.0051', '5238083-94.2024.8.09.0051', '5292555-50.2021.8.09.0051', ' \n5140415-65.2020.8.09.0051']
        resultados = []

        try:
            # Login com retry
            print("FAZENDO LOGIN...")
            for tentativa in range(3):
                try:
                    self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200", reconnect_time=5)
                    self.sleep(3)

                    # Verificar se a página carregou
                    if not self.is_element_present('//*[@id="login"]'):
                        print(f"AVISO: Tentativa {tentativa + 1}: Página não carregou corretamente")
                        continue

                    self.type('//*[@id="login"]', "07228313151")
                    self.sleep(1)
                    self.type('//*[@id="senha"]', "Senhaprojudi24.")
                    self.sleep(1)
                    self.click('//*[@id="formLogin"]/div[4]/input[1]')
                    self.sleep(5)

                    # Verificar login
                    if self.is_element_present('//*[@id="menuPrinciapl"]/ul[2]/li'):
                        print("SUCESSO: Login realizado com sucesso!")
                        break
                    else:
                        print(f"AVISO: Tentativa {tentativa + 1}: Login falhou")

                except Exception as e:
                    print(f"AVISO: Tentativa {tentativa + 1} falhou: {e}")
                    if tentativa == 2:
                        raise Exception("Falha no login após 3 tentativas")

            # Processar cada processo
            for i, processo in enumerate(processos, 1):
                print(f"PROCESSANDO {i}/{len(processos)}: {processo}")

                resultado = {
                    'Número do Processo': processo,
                    'Processo Encontrado': 'Não',
                    'Nome Polo Ativo': '',
                    'CPF Polo Ativo': ''
                }

                try:
                    # Verificar se o browser ainda está ativo
                    try:
                        current_handle = self.driver.current_window_handle
                    except:
                        print("ERRO: Browser foi fechado! Tentando reconectar...")
                        self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
                        continue

                    # Navegar para busca com retry
                    for tentativa_busca in range(2):
                        try:
                            self.uc_open_with_reconnect("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24", reconnect_time=3)
                            self.sleep(2)

                            if self.is_element_present('//*[@id="ProcessoNumero"]'):
                                break
                        except:
                            if tentativa_busca == 1:
                                print(f"ERRO: Falha ao carregar página de busca para {processo}")
                                continue

                    # Inserir número do processo
                    self.type('//*[@id="ProcessoNumero"]', str(processo))
                    self.sleep(1)

                    # Clicar em mostrar todos os processos
                    try:
                        if self.is_element_present('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]'):
                            self.click('/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]')
                            self.sleep(1)
                    except:
                        pass

                    # Clicar em pesquisar
                    self.click('//*[@id="divBotoesCentralizados"]/input[1]')
                    self.sleep(4)

                    # Verificar resultado
                    try:
                        page_source = self.get_page_source()

                        # Verificar se há mensagem de acesso predatório
                        if "predatório" in page_source.lower() or "predatory" in page_source.lower():
                            print(f"ERRO: Processo {processo}: ACESSO PREDATORIO DETECTADO!")
                            resultado['Processo Encontrado'] = 'Erro - Acesso Predatorio'
                        elif "Polo Ativo" in page_source or self.is_text_visible("Polo Ativo"):
                            resultado['Processo Encontrado'] = 'Sim'
                            print(f"SUCESSO: Processo {processo}: Encontrado!")

                            # Extrair dados básicos
                            try:
                                spans = self.find_elements("span")
                                for span in spans:
                                    text = span.text.strip()
                                    if text and len(text) > 5:
                                        if not resultado['Nome Polo Ativo'] and not any(char.isdigit() for char in text[:3]):
                                            resultado['Nome Polo Ativo'] = text
                                        elif "." in text and "-" in text and len(text) >= 11:
                                            resultado['CPF Polo Ativo'] = text
                            except:
                                pass
                        else:
                            print(f"INFO: Processo {processo}: Não encontrado")

                    except Exception as e:
                        print(f"ERRO: Erro ao verificar resultado para {processo}: {e}")

                except Exception as e:
                    print(f"ERRO: Erro geral no processo {processo}: {e}")

                resultados.append(resultado)

                # Salvar a cada 5 processos
                if i % 5 == 0:
                    self.salvar_resultados(resultados)

            # Salvar resultados finais
            self.salvar_resultados(resultados)
            print("CONCLUIDO: Processamento finalizado!")

        except Exception as e:
            print(f"ERRO CRITICO: {e}")
            if resultados:
                self.salvar_resultados(resultados)

    def salvar_resultados(self, resultados):
        """Salva os resultados em Excel"""
        try:
            df_resultado = pd.DataFrame(resultados)
            nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            df_resultado.to_excel(nome_arquivo, index=False)
            print(f"SALVANDO: Resultados salvos em: {nome_arquivo}")
        except Exception as e:
            print(f"ERRO: Erro ao salvar: {e}")
