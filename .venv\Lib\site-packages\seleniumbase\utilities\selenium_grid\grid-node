#!/usr/bin/env bash

#
# Usage: grid-node {start|stop}
#

source $(dirname $0)/font_color

EXPECTED_ARGS=1
E_BADARGS=65

DO_showUsage() {
    echo "Usage: $(basename $0) {start|stop}"
    exit $E_BADARGS
}

if [ $# -ne $EXPECTED_ARGS ]; then
    DO_showUsage
fi

################################################################################

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null && pwd )"
GRID_HUB_SERVER_IP_FILE=${DIR}/ip_of_grid_hub.dat
GRID_NODE_VERBOSE_LOGS_FILE=${DIR}/verbose_node_server.dat

GRID_HUB_SERVER_IP="127.0.0.1"
if [ -f $GRID_HUB_SERVER_IP_FILE ]; then
    GRID_HUB_SERVER_IP=$(cat $GRID_HUB_SERVER_IP_FILE)
fi

GRID_NODE_VERBOSE_LOGS="False"
if [ -f $GRID_NODE_VERBOSE_LOGS_FILE ]; then
    GRID_NODE_VERBOSE_LOGS=$(cat $GRID_NODE_VERBOSE_LOGS_FILE)
fi

VERBOSITY_STRING="-Djava.util.logging.config.file=test/logging.properties "
if [ "$GRID_NODE_VERBOSE_LOGS" == "True" ]; then
  VERBOSITY_STRING=""
fi

WEBDRIVER_SERVER_JAR=${DIR}/selenium-server-standalone.jar
WEBDRIVER_NODE_PARAMS="-role node -hub http://${GRID_HUB_SERVER_IP}:4444/grid/register -browser browserName=chrome,maxInstances=5,version=latest,seleniumProtocol=WebDriver -browser browserName=firefox,maxInstances=5,version=latest,seleniumProtocol=WebDriver"
WEBDRIVER_NODE_PIDFILE="/tmp/webdriver_node.pid"

if [ ! -f $WEBDRIVER_SERVER_JAR ]; then
    echo "You must place the Selenium-WebDriver standalone JAR file at ${WEBDRIVER_SERVER_JAR} before proceeding."
    exit 1
fi

case "$1" in
    start)
        echo "Starting Selenium-WebDriver Grid node..."
        if [ -f $WEBDRIVER_NODE_PIDFILE ]; then
            echo "${FAIL_MSG} Selenium-WebDriver Grid node already running with PID $(cat $WEBDRIVER_NODE_PIDFILE). Run 'grid-node stop' or 'grid-node restart'."
            exit 1
        else
            START_NODE_CMD="java ${VERBOSITY_STRING}-jar ${WEBDRIVER_SERVER_JAR} ${WEBDRIVER_NODE_PARAMS}"
            echo ""
            echo $START_NODE_CMD
            echo ""
            $START_NODE_CMD &
            PID=$!
            echo $PID > "${WEBDRIVER_NODE_PIDFILE}"
            echo "${SUCCESS_MSG} Selenium-WebDriver Grid node started successfully."
            echo ""
            # echo "To see full log output, remove the java.util.logging.config.file parameter from script/grid-node"
        fi
        ;;
    stop)
        echo "Stopping Selenium-WebDriver Grid node..."
        if [ -f $WEBDRIVER_NODE_PIDFILE ]; then
            PID=$(cat $WEBDRIVER_NODE_PIDFILE)
            rm $WEBDRIVER_NODE_PIDFILE
            kill $PID
            sleep 1
            if [[ $(ps -A | egrep "^${PID}") ]]; then
                echo "${FAIL_MSG} Tried to kill the node with PID ${PID}, but was unsuccessful. You need to kill it with something stronger, like 'kill -9'"
                exit 1
            else
                echo "${SUCCESS_MSG} Selenium-WebDriver Grid node stopped successfully."
                exit 0
            fi
        else
            echo "${SUCCESS_MSG} Selenium-WebDriver Grid node has already been stopped."
            exit 0
        fi
        ;;
    restart)
        $0 stop
        $0 start
        ;;
    *)
        DO_showUsage
esac
